import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { emailTemplateDb } from '@/lib/database';
import { ErrorLogger } from '@/lib/errorLogger';

const defaultTemplates = [
  {
    name: 'otp_verification',
    subject: 'Email Verification - HashCoreX',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Sustainable Mining Platform</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Thank you for registering with HashCoreX. To complete your registration, please verify your email address using the OTP code below:</p>

        <div style="background: #fff; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;">
            <h3 style="margin: 0; color: #667eea; font-size: 32px; letter-spacing: 5px;">{{otp}}</h3>
            <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">This code expires in 10 minutes</p>
        </div>

        <p>If you didn't create an account with HashCoreX, please ignore this email.</p>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #667eea; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #667eea; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,
    textContent: `Hello {{firstName}}!

Thank you for registering with HashCoreX. To complete your registration, please verify your email address using the OTP code below:

Your OTP Code: {{otp}}

This code expires in 10 minutes.

If you didn't create an account with HashCoreX, please ignore this email.

Best regards,
The HashCoreX Team`
  },
  {
    name: 'welcome_email',
    subject: 'Welcome to HashCoreX - Your Mining Journey Begins!',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to HashCoreX</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to HashCoreX!</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Your Sustainable Mining Journey Starts Here</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Congratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable mining community.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #667eea;">
            <h3 style="margin-top: 0; color: #667eea;">What's Next?</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Complete your KYC verification</li>
                <li>Explore our mining packages</li>
                <li>Start earning with sustainable mining</li>
                <li>Refer friends and earn bonuses</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Access Dashboard</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Need help? Contact our support team anytime.</p>
            <p>Best regards,<br>The HashCoreX Team</p>
        </div>
    </div>
</body>
</html>`,
    textContent: `Hello {{firstName}}!

Congratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable mining community.

What's Next?
- Complete your KYC verification
- Explore our mining packages
- Start earning with sustainable mining
- Refer friends and earn bonuses

Visit your dashboard to get started: [Dashboard Link]

Need help? Contact our support team anytime.

Best regards,
The HashCoreX Team`
  },
  {
    name: 'deposit_success',
    subject: 'Deposit Confirmed - HashCoreX',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deposit Confirmed</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Deposit Confirmed!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Great news! Your deposit has been successfully confirmed and credited to your account.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">Deposit Details</h3>
            <p><strong>Amount:</strong> {{amount}} {{currency}}</p>
            <p><strong>Transaction ID:</strong> {{transactionId}}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Confirmed</span></p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View Dashboard</a>
        </div>

        <p>You can now use these funds to purchase mining packages and start earning!</p>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,
    textContent: `Hello {{firstName}}!

Great news! Your deposit has been successfully confirmed and credited to your account.

Deposit Details:
- Amount: {{amount}} {{currency}}
- Transaction ID: {{transactionId}}
- Status: Confirmed

You can now use these funds to purchase mining packages and start earning!

Visit your dashboard to get started: [Dashboard Link]

Best regards,
The HashCoreX Team`
  },
  {
    name: 'kyc_approved',
    subject: 'KYC Verification Approved - HashCoreX',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Approved</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">KYC Verification Approved!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Congratulations! Your KYC verification has been approved. Your account is now fully verified and you have access to all HashCoreX features.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">What's Now Available?</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Full access to mining packages</li>
                <li>Unlimited deposits and withdrawals</li>
                <li>Access to premium features</li>
                <li>Higher referral commissions</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Access Dashboard</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,
    textContent: `Hello {{firstName}}!

Congratulations! Your KYC verification has been approved. Your account is now fully verified and you have access to all HashCoreX features.

What's Now Available?
- Full access to mining packages
- Unlimited deposits and withdrawals
- Access to premium features
- Higher referral commissions

Visit your dashboard to get started: [Dashboard Link]

Best regards,
The HashCoreX Team`
  },
  {
    name: 'kyc_rejected',
    subject: 'KYC Verification Update - HashCoreX',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Update</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">KYC Verification Update</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>We have reviewed your KYC verification documents. Unfortunately, we need additional information to complete your verification.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;">
            <h3 style="margin-top: 0; color: #ef4444;">Reason for Review</h3>
            <p>{{rejectionReason}}</p>
        </div>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #667eea;">
            <h3 style="margin-top: 0; color: #667eea;">Next Steps</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Review the feedback above</li>
                <li>Prepare updated documents</li>
                <li>Resubmit your KYC application</li>
                <li>Contact support if you need help</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Resubmit KYC</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #667eea; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #667eea; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,
    textContent: `Hello {{firstName}}!

We have reviewed your KYC verification documents. Unfortunately, we need additional information to complete your verification.

Reason for Review:
{{rejectionReason}}

Next Steps:
- Review the feedback above
- Prepare updated documents
- Resubmit your KYC application
- Contact support if you need help

Visit your dashboard to resubmit: [Dashboard Link]

Best regards,
The HashCoreX Team`
  },
  {
    name: 'withdrawal_approved',
    subject: 'Withdrawal Approved - HashCoreX',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawal Approved</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Withdrawal Approved!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Good news! Your withdrawal request has been approved and is being processed.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">Withdrawal Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Destination:</strong> {{usdtAddress}}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Approved - Processing</span></p>
        </div>

        <p>Your funds will be transferred to your wallet within 24 hours. You will receive another email once the transaction is completed.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View Transaction</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,
    textContent: `Hello {{firstName}}!

Good news! Your withdrawal request has been approved and is being processed.

Withdrawal Details:
- Amount: {{amount}} USDT
- Destination: {{usdtAddress}}
- Status: Approved - Processing

Your funds will be transferred to your wallet within 24 hours. You will receive another email once the transaction is completed.

Visit your dashboard: [Dashboard Link]

Best regards,
The HashCoreX Team`
  },
  {
    name: 'withdrawal_completed',
    subject: 'Withdrawal Completed - HashCoreX',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawal Completed</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Withdrawal Completed!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Your withdrawal has been successfully completed! The funds have been transferred to your wallet.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">Transaction Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Destination:</strong> {{usdtAddress}}</p>
            <p><strong>Transaction Hash:</strong> {{transactionHash}}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Completed</span></p>
        </div>

        <p>You can verify this transaction on the blockchain using the transaction hash above.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View on Blockchain</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,
    textContent: `Hello {{firstName}}!

Your withdrawal has been successfully completed! The funds have been transferred to your wallet.

Transaction Details:
- Amount: {{amount}} USDT
- Destination: {{usdtAddress}}
- Transaction Hash: {{transactionHash}}
- Status: Completed

You can verify this transaction on the blockchain using the transaction hash above.

Visit your dashboard: [Dashboard Link]

Best regards,
The HashCoreX Team`
  },
  {
    name: 'withdrawal_rejected',
    subject: 'Withdrawal Update - HashCoreX',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawal Update</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Withdrawal Update</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>We regret to inform you that your withdrawal request could not be processed at this time.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;">
            <h3 style="margin-top: 0; color: #ef4444;">Withdrawal Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Reason:</strong> {{rejectionReason}}</p>
        </div>

        <p>The requested amount has been returned to your account balance. Please review the reason above and contact our support team if you need assistance.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Contact Support</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #667eea; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #667eea; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,
    textContent: `Hello {{firstName}}!

We regret to inform you that your withdrawal request could not be processed at this time.

Withdrawal Details:
- Amount: {{amount}} USDT
- Reason: {{rejectionReason}}

The requested amount has been returned to your account balance. Please review the reason above and contact our support team if you need assistance.

Visit your dashboard: [Dashboard Link]

Best regards,
The HashCoreX Team`
  },
  {
    name: 'test_email',
    subject: 'HashCoreX Email Test',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Test</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Email Configuration Test</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Email Test Successful!</h2>

        <p>This is a test email to verify your SMTP configuration.</p>
        <p>If you received this email, your email settings are working correctly!</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #667eea;">
            <h3 style="margin-top: 0; color: #667eea;">Test Details</h3>
            <p><strong>Test Date:</strong> {{testDate}}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Success</span></p>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Your email configuration is working properly.</p>
            <p>Best regards,<br>The HashCoreX Team</p>
        </div>
    </div>
</body>
</html>`,
    textContent: `HashCoreX Email Test

This is a test email to verify your SMTP configuration.
If you received this email, your email settings are working correctly!

Test Date: {{testDate}}
Status: Success

Your email configuration is working properly.

Best regards,
The HashCoreX Team`
  },
  {
    name: 'password_reset_otp',
    subject: 'Password Reset - HashCoreX',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Password Reset Request</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>We received a request to reset your password for your HashCoreX account. If you made this request, please use the verification code below:</p>

        <div style="background: #fff; padding: 30px; margin: 30px 0; border-radius: 10px; text-align: center; border: 2px solid #ef4444;">
            <h2 style="color: #ef4444; margin: 0 0 10px 0; font-size: 36px; letter-spacing: 8px; font-weight: bold;">{{otp}}</h2>
            <p style="color: #666; margin: 0; font-size: 14px;">This code will expire in 10 minutes</p>
        </div>

        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #856404; font-size: 14px;">
                <strong>Security Notice:</strong> If you didn't request this password reset, please ignore this email. Your account remains secure.
            </p>
        </div>

        <p>For your security:</p>
        <ul style="color: #666; font-size: 14px;">
            <li>This code is valid for 10 minutes only</li>
            <li>Don't share this code with anyone</li>
            <li>If you didn't request this reset, your account is still secure</li>
        </ul>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Need help? Contact our support team.</p>
            <p>Best regards,<br>The HashCoreX Team</p>
        </div>
    </div>
</body>
</html>`,
    textContent: `HashCoreX Password Reset

Hello {{firstName}}!

We received a request to reset your password for your HashCoreX account. If you made this request, please use the verification code below:

Your verification code: {{otp}}

This code will expire in 10 minutes.

Security Notice: If you didn't request this password reset, please ignore this email. Your account remains secure.

For your security:
- This code is valid for 10 minutes only
- Don't share this code with anyone
- If you didn't request this reset, your account is still secure

Need help? Contact our support team.

Best regards,
The HashCoreX Team`
  }];

// POST - Seed default email templates
export async function POST(request: NextRequest) {
  try {
    // Authenticate admin
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isUserAdmin = await isAdmin(user.id);
    if (!isUserAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const results = [];

    for (const template of defaultTemplates) {
      try {
        // Check if template already exists
        const existing = await emailTemplateDb.findByName(template.name);
        if (existing) {
          results.push({ name: template.name, status: 'exists' });
          continue;
        }

        // Create template
        await emailTemplateDb.create(template);
        results.push({ name: template.name, status: 'created' });
      } catch (error) {
        results.push({ name: template.name, status: 'error', error: (error as Error).message });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Default templates seeded successfully',
      data: results,
    });

  } catch (error) {
    console.error('Seed email templates error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'SEED_EMAIL_TEMPLATES_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
