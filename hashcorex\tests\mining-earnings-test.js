/**
 * Manual Test Script for Mining Earnings and Wallet Functions
 * 
 * This script tests the complete flow of:
 * 1. Mining earnings calculation and crediting to pending balance
 * 2. Pending balance to wallet transfer functionality
 * 3. Admin configuration integration
 * 
 * Run this script manually to verify the mining system is working correctly.
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Test configuration
const TEST_USER_ID = 'test-user-mining-earnings';
const TEST_EMAIL = '<EMAIL>';

async function createTestUser() {
  console.log('🔧 Creating test user...');
  
  try {
    // Delete existing test user if exists
    await prisma.user.deleteMany({
      where: { email: TEST_EMAIL }
    });

    // Create test user
    const user = await prisma.user.create({
      data: {
        id: TEST_USER_ID,
        email: TEST_EMAIL,
        firstName: 'Test',
        lastName: 'User',
        isActive: true,
        emailVerified: true,
        kycStatus: 'APPROVED'
      }
    });

    // Create wallet balance
    await prisma.walletBalance.create({
      data: {
        userId: user.id,
        availableBalance: 1000.00, // Starting balance for testing
        pendingBalance: 0,
        totalDeposits: 1000.00,
        totalWithdrawals: 0,
        totalEarnings: 0
      }
    });

    console.log('✅ Test user created successfully');
    return user;
  } catch (error) {
    console.error('❌ Error creating test user:', error);
    throw error;
  }
}

async function createTestMiningUnit(userId, thsAmount = 5.0, investmentAmount = 250.0) {
  console.log(`🔧 Creating test mining unit (${thsAmount} TH/s, $${investmentAmount})...`);
  
  try {
    const expiryDate = new Date();
    expiryDate.setMonth(expiryDate.getMonth() + 24); // 24 months from now

    const miningUnit = await prisma.miningUnit.create({
      data: {
        userId,
        thsAmount,
        investmentAmount,
        dailyROI: 0.43, // Test with 0.43% daily ROI
        expiryDate,
        status: 'ACTIVE',
        miningEarnings: 0,
        referralEarnings: 0,
        binaryEarnings: 0,
        totalEarned: 0
      }
    });

    console.log('✅ Test mining unit created:', {
      id: miningUnit.id,
      thsAmount: miningUnit.thsAmount,
      investmentAmount: miningUnit.investmentAmount,
      dailyROI: miningUnit.dailyROI
    });

    return miningUnit;
  } catch (error) {
    console.error('❌ Error creating test mining unit:', error);
    throw error;
  }
}

async function testDailyMiningEarnings(userId) {
  console.log('🧪 Testing daily mining earnings calculation...');
  
  try {
    // Import mining functions
    const { processDailyEarnings } = require('../src/lib/mining');
    
    // Get user's mining units before processing
    const miningUnitsBefore = await prisma.miningUnit.findMany({
      where: { userId, status: 'ACTIVE' }
    });

    console.log('Mining units before processing:', miningUnitsBefore.map(unit => ({
      id: unit.id,
      thsAmount: unit.thsAmount,
      investmentAmount: unit.investmentAmount,
      dailyROI: unit.dailyROI,
      miningEarnings: unit.miningEarnings
    })));

    // Process daily earnings
    const results = await processDailyEarnings();
    
    // Get user's mining units after processing
    const miningUnitsAfter = await prisma.miningUnit.findMany({
      where: { userId, status: 'ACTIVE' }
    });

    // Get pending transactions
    const pendingTransactions = await prisma.transaction.findMany({
      where: {
        userId,
        type: 'MINING_EARNINGS',
        status: 'PENDING'
      }
    });

    console.log('✅ Daily earnings processing results:', {
      processedUsers: results.length,
      userResult: results.find(r => r.userId === userId),
      pendingTransactions: pendingTransactions.length,
      miningUnitsAfter: miningUnitsAfter.map(unit => ({
        id: unit.id,
        miningEarnings: unit.miningEarnings,
        totalEarned: unit.totalEarned
      }))
    });

    return { results, pendingTransactions };
  } catch (error) {
    console.error('❌ Error testing daily mining earnings:', error);
    throw error;
  }
}

async function testWeeklyEarningsDistribution(userId) {
  console.log('🧪 Testing weekly earnings distribution...');
  
  try {
    // Import mining functions
    const { processWeeklyEarnings } = require('../src/lib/mining');
    
    // Get wallet balance before distribution
    const walletBefore = await prisma.walletBalance.findUnique({
      where: { userId }
    });

    // Get pending transactions before distribution
    const pendingBefore = await prisma.transaction.findMany({
      where: {
        userId,
        type: 'MINING_EARNINGS',
        status: 'PENDING'
      }
    });

    console.log('Before weekly distribution:', {
      walletBalance: walletBefore?.availableBalance,
      pendingBalance: walletBefore?.pendingBalance,
      pendingTransactions: pendingBefore.length
    });

    // Process weekly earnings distribution
    const results = await processWeeklyEarnings();
    
    // Get wallet balance after distribution
    const walletAfter = await prisma.walletBalance.findUnique({
      where: { userId }
    });

    // Get completed transactions after distribution
    const completedAfter = await prisma.transaction.findMany({
      where: {
        userId,
        type: 'MINING_EARNINGS',
        status: 'COMPLETED'
      }
    });

    console.log('✅ Weekly distribution results:', {
      processedUsers: results.length,
      walletAfter: {
        availableBalance: walletAfter?.availableBalance,
        pendingBalance: walletAfter?.pendingBalance,
        totalEarnings: walletAfter?.totalEarnings
      },
      completedTransactions: completedAfter.length
    });

    return { results, walletAfter, completedAfter };
  } catch (error) {
    console.error('❌ Error testing weekly earnings distribution:', error);
    throw error;
  }
}

async function testAdminConfiguration() {
  console.log('🧪 Testing admin configuration integration...');
  
  try {
    // Import admin settings and mining functions
    const { adminSettingsDb } = require('../src/lib/database');
    const { calculateDynamicROI, getEarningsRangeForTHS } = require('../src/lib/mining');
    
    // Test earnings ranges configuration
    const earningsRangesStr = await adminSettingsDb.get('earningsRanges');
    let earningsRanges = [];
    
    if (earningsRangesStr) {
      earningsRanges = JSON.parse(earningsRangesStr);
    }

    console.log('Current earnings ranges configuration:', earningsRanges);

    // Test dynamic ROI calculation for different TH/s amounts
    const testAmounts = [2.0, 5.0, 15.0, 60.0];
    
    for (const thsAmount of testAmounts) {
      const dynamicROI = await calculateDynamicROI(thsAmount);
      const range = getEarningsRangeForTHS ? getEarningsRangeForTHS(thsAmount) : null;
      
      console.log(`TH/s: ${thsAmount} -> Dynamic ROI: ${dynamicROI}%`, range ? `(Range: ${range.dailyReturnMin}-${range.dailyReturnMax}%)` : '');
    }

    console.log('✅ Admin configuration test completed');
    return { earningsRanges };
  } catch (error) {
    console.error('❌ Error testing admin configuration:', error);
    throw error;
  }
}

async function testPendingToWalletTransfer(userId) {
  console.log('🧪 Testing pending balance to wallet transfer...');
  
  try {
    // Get current wallet state
    const walletBefore = await prisma.walletBalance.findUnique({
      where: { userId }
    });

    // Get all pending mining earnings
    const pendingTransactions = await prisma.transaction.findMany({
      where: {
        userId,
        type: 'MINING_EARNINGS',
        status: 'PENDING'
      }
    });

    const totalPendingAmount = pendingTransactions.reduce((sum, tx) => sum + tx.amount, 0);

    console.log('Before transfer:', {
      availableBalance: walletBefore?.availableBalance,
      pendingBalance: walletBefore?.pendingBalance,
      totalPendingTransactions: pendingTransactions.length,
      totalPendingAmount
    });

    // Simulate weekly earnings distribution (this moves pending to available)
    if (pendingTransactions.length > 0) {
      const { processWeeklyEarnings } = require('../src/lib/mining');
      await processWeeklyEarnings();
    }

    // Get wallet state after transfer
    const walletAfter = await prisma.walletBalance.findUnique({
      where: { userId }
    });

    console.log('✅ After transfer:', {
      availableBalance: walletAfter?.availableBalance,
      pendingBalance: walletAfter?.pendingBalance,
      balanceIncrease: (walletAfter?.availableBalance || 0) - (walletBefore?.availableBalance || 0)
    });

    return { walletBefore, walletAfter, totalPendingAmount };
  } catch (error) {
    console.error('❌ Error testing pending to wallet transfer:', error);
    throw error;
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  
  try {
    // Delete test user and related data (cascade will handle related records)
    await prisma.user.deleteMany({
      where: { email: TEST_EMAIL }
    });

    console.log('✅ Test data cleaned up');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
  }
}

async function runFullTest() {
  console.log('🚀 Starting Mining Earnings and Wallet Functions Test\n');
  
  try {
    // Step 1: Create test user and mining unit
    const user = await createTestUser();
    const miningUnit = await createTestMiningUnit(user.id);

    // Step 2: Test admin configuration
    await testAdminConfiguration();

    // Step 3: Test daily mining earnings calculation
    await testDailyMiningEarnings(user.id);

    // Step 4: Test pending to wallet transfer
    await testPendingToWalletTransfer(user.id);

    // Step 5: Test weekly earnings distribution
    await testWeeklyEarningsDistribution(user.id);

    console.log('\n✅ All tests completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  } finally {
    // Cleanup
    await cleanupTestData();
    await prisma.$disconnect();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  runFullTest();
}

module.exports = {
  createTestUser,
  createTestMiningUnit,
  testDailyMiningEarnings,
  testWeeklyEarningsDistribution,
  testAdminConfiguration,
  testPendingToWalletTransfer,
  cleanupTestData,
  runFullTest
};
