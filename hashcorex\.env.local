# Database Configuration
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/hashcorex?schema=public"
DIRECT_URL="postgresql://postgres:postgres@localhost:5432/hashcorex?schema=public"

# JWT Configuration
JWT_SECRET="super-secret-jwt-key-for-development-testing-only-change-in-production"
JWT_EXPIRES_IN="30d"

# Encryption Keys
ENCRYPTION_KEY="development-encryption-key-32chars"

# Admin Configuration
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="change-this-secure-password"

# Platform Settings
NEXT_PUBLIC_APP_NAME="HashCoreX"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Mining Configuration
DEFAULT_THS_PRICE="50"
DEFAULT_ROI_MIN="0.6"
DEFAULT_ROI_MAX="1.1"
MINIMUM_PURCHASE="50"
MINIMUM_WITHDRAWAL="10"

# Binary System Configuration
DIRECT_REFERRAL_BONUS="10"
BINARY_POOL_PERCENTAGE="30"
MAX_BINARY_POINTS_PER_SIDE="2000"

# USDT Configuration (for withdrawals)
USDT_NETWORK="TRC20"

# Trongrid API Configuration
TRONGRID_API_KEY="19e046a0-f3b0-4253-bdaf-352f61f66ec8"
TRONGRID_API_URL="https://api.trongrid.io"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Development
NODE_ENV="development"
