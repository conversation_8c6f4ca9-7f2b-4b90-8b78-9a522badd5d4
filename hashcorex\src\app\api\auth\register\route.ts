import { NextRequest, NextResponse } from 'next/server';
import { registerUser, validateEmail, generateToken } from '@/lib/auth';
import { validatePassword } from '@/lib/utils';
import { systemLogDb, otpDb } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, firstName, lastName, password, confirmPassword, referralCode, otp } = body;

    // Validation
    if (!email || !firstName || !lastName || !password || !confirmPassword || !otp) {
      return NextResponse.json(
        { success: false, error: 'All fields including OTP are required' },
        { status: 400 }
      );
    }

    if (!validateEmail(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    if (password !== confirmPassword) {
      return NextResponse.json(
        { success: false, error: 'Passwords do not match' },
        { status: 400 }
      );
    }

    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { success: false, error: passwordValidation.errors.join(', ') },
        { status: 400 }
      );
    }

    // Verify OTP before registration
    const otpRecord = await otpDb.findValid(email, 'email_verification');
    if (!otpRecord || otpRecord.otp !== otp) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired OTP. Please request a new one.' },
        { status: 400 }
      );
    }

    // Mark OTP as verified
    await otpDb.verify(otpRecord.id);

    // Extract side parameter from URL if present
    const url = new URL(request.url);
    const side = url.searchParams.get('side') as 'left' | 'right' | null;

    // Register user
    const user = await registerUser({
      email,
      firstName,
      lastName,
      password,
      referralCode,
      placementSide: side || undefined,
    });

    // Log registration
    await systemLogDb.create({
      action: 'USER_REGISTERED',
      userId: user.id,
      details: {
        email: user.email,
        referralCode: referralCode || null,
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    // Generate token for automatic login after registration
    const token = generateToken({
      userId: user.id,
      email: user.email,
    });

    // Set HTTP-only cookie for automatic login
    const response = NextResponse.json({
      success: true,
      message: 'Registration successful',
      data: {
        user,
        token,
      },
    });

    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: '/',
    });

    return response;

  } catch (error: any) {
    console.error('Registration error:', error);
    
    return NextResponse.json(
      { success: false, error: error.message || 'Registration failed' },
      { status: 400 }
    );
  }
}
