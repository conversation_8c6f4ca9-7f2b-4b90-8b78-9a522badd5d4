"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_mining_ts";
exports.ids = ["_rsc_src_lib_mining_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/mining.ts":
/*!***************************!*\
  !*** ./src/lib/mining.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDailyROI: () => (/* binding */ calculateDailyROI),\n/* harmony export */   calculateDynamicROI: () => (/* binding */ calculateDynamicROI),\n/* harmony export */   calculateEstimatedEarnings: () => (/* binding */ calculateEstimatedEarnings),\n/* harmony export */   expireOldMiningUnits: () => (/* binding */ expireOldMiningUnits),\n/* harmony export */   getMiningStats: () => (/* binding */ getMiningStats),\n/* harmony export */   getMonthlyReturnLimits: () => (/* binding */ getMonthlyReturnLimits),\n/* harmony export */   processWeeklyEarnings: () => (/* binding */ processWeeklyEarnings),\n/* harmony export */   updateExistingMiningUnitsROI: () => (/* binding */ updateExistingMiningUnitsROI),\n/* harmony export */   validateMonthlyReturn: () => (/* binding */ validateMonthlyReturn)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./miningUnitEarnings */ \"(rsc)/./src/lib/miningUnitEarnings.ts\");\n\n\n\n// Note: User active status is now handled dynamically in the referral system\n// The isActive field in the database is only used for account access control\n// Mining activity status is computed on-demand for binary tree display\n// Calculate dynamic ROI based on TH/s amount and admin-configured ranges\nasync function calculateDynamicROI(thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        let minROI;\n        let maxROI;\n        if (applicableRange) {\n            minROI = applicableRange.dailyReturnMin;\n            maxROI = applicableRange.dailyReturnMax;\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            minROI = highestRange.dailyReturnMin;\n            maxROI = highestRange.dailyReturnMax;\n        }\n        // Add randomization within the range\n        const randomROI = minROI + Math.random() * (maxROI - minROI);\n        // Round to 2 decimal places\n        return Math.round(randomROI * 100) / 100;\n    } catch (error) {\n        console.error('Error calculating dynamic ROI:', error);\n        // Fallback to default values based on TH/s amount\n        if (thsAmount >= 50) return 0.6;\n        if (thsAmount >= 10) return 0.5;\n        return 0.4;\n    }\n}\n// Validate monthly return doesn't exceed admin-configured limits for specific TH/s amount\nasync function validateMonthlyReturn(dailyROI, thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        let monthlyMin = 10.0;\n        let monthlyMax = 15.0;\n        if (applicableRange) {\n            monthlyMin = applicableRange.monthlyReturnMin || 10.0;\n            monthlyMax = applicableRange.monthlyReturnMax || 15.0;\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            monthlyMin = highestRange.monthlyReturnMin || 10.0;\n            monthlyMax = highestRange.monthlyReturnMax || 15.0;\n        }\n        const monthlyReturn = dailyROI * 30; // Approximate monthly return\n        return monthlyReturn >= monthlyMin && monthlyReturn <= monthlyMax;\n    } catch (error) {\n        console.error('Error validating monthly return:', error);\n        // Fallback to default 10-15% range\n        const monthlyReturn = dailyROI * 30;\n        return monthlyReturn >= 10 && monthlyReturn <= 15;\n    }\n}\n// Get monthly return limits for a specific TH/s amount\nasync function getMonthlyReturnLimits(thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        if (applicableRange) {\n            return {\n                min: applicableRange.monthlyReturnMin || 10.0,\n                max: applicableRange.monthlyReturnMax || 15.0\n            };\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            return {\n                min: highestRange.monthlyReturnMin || 10.0,\n                max: highestRange.monthlyReturnMax || 15.0\n            };\n        }\n    } catch (error) {\n        console.error('Error getting monthly return limits:', error);\n        // Fallback to default 10-15% range\n        return {\n            min: 10.0,\n            max: 15.0\n        };\n    }\n}\n// Update existing mining units when earnings configuration changes\nasync function updateExistingMiningUnitsROI() {\n    try {\n        console.log('Updating existing mining units with new ROI configuration...');\n        // Get all active mining units\n        const activeMiningUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n        console.log(`Found ${activeMiningUnits.length} active mining units to update`);\n        const updateResults = [];\n        for (const unit of activeMiningUnits){\n            try {\n                // Calculate new ROI based on current TH/s amount\n                const newROI = await calculateDynamicROI(unit.thsAmount);\n                // Update the mining unit with new ROI\n                await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n                    where: {\n                        id: unit.id\n                    },\n                    data: {\n                        dailyROI: newROI\n                    }\n                });\n                updateResults.push({\n                    unitId: unit.id,\n                    userId: unit.userId,\n                    thsAmount: unit.thsAmount,\n                    oldROI: unit.dailyROI,\n                    newROI\n                });\n                console.log(`Updated unit ${unit.id}: ${unit.dailyROI}% -> ${newROI}%`);\n            } catch (unitError) {\n                console.error(`Error updating unit ${unit.id}:`, unitError);\n            }\n        }\n        // Log the update process\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'MINING_UNITS_ROI_UPDATED',\n            details: {\n                unitsUpdated: updateResults.length,\n                totalUnits: activeMiningUnits.length,\n                updateResults,\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Successfully updated ${updateResults.length} mining units with new ROI configuration`);\n        return {\n            success: true,\n            unitsUpdated: updateResults.length,\n            totalUnits: activeMiningUnits.length,\n            updateResults\n        };\n    } catch (error) {\n        console.error('Error updating existing mining units ROI:', error);\n        throw error;\n    }\n}\n// Calculate daily ROI for all active mining units using FIFO allocation\nasync function calculateDailyROI() {\n    try {\n        console.log('Starting daily ROI calculation with FIFO allocation...');\n        // Get all users with active mining units\n        const usersWithMiningUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findMany({\n            where: {\n                miningUnits: {\n                    some: {\n                        status: 'ACTIVE',\n                        expiryDate: {\n                            gt: new Date()\n                        }\n                    }\n                }\n            },\n            include: {\n                miningUnits: {\n                    where: {\n                        status: 'ACTIVE',\n                        expiryDate: {\n                            gt: new Date()\n                        }\n                    }\n                }\n            }\n        });\n        console.log(`Found ${usersWithMiningUnits.length} users with active mining units`);\n        const results = [];\n        for (const user of usersWithMiningUnits){\n            try {\n                // Calculate total daily earnings for this user\n                let totalDailyEarnings = 0;\n                const unitEarnings = [];\n                for (const unit of user.miningUnits){\n                    const dailyEarnings = unit.investmentAmount * unit.dailyROI / 100;\n                    totalDailyEarnings += dailyEarnings;\n                    unitEarnings.push({\n                        unitId: unit.id,\n                        thsAmount: unit.thsAmount,\n                        dailyEarnings\n                    });\n                }\n                if (totalDailyEarnings > 0) {\n                    // Create transaction first\n                    const transaction = await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.create({\n                        userId: user.id,\n                        type: 'MINING_EARNINGS',\n                        amount: totalDailyEarnings,\n                        description: `Daily mining earnings - Total: ${unitEarnings.map((u)=>`${u.thsAmount} TH/s`).join(', ')}`,\n                        status: 'PENDING'\n                    });\n                    // Allocate earnings to mining units using FIFO logic\n                    const allocations = await (0,_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__.allocateEarningsToUnits)(user.id, totalDailyEarnings, 'MINING_EARNINGS', transaction.id, 'Daily mining ROI earnings');\n                    results.push({\n                        userId: user.id,\n                        totalEarnings: totalDailyEarnings,\n                        allocations,\n                        unitsProcessed: user.miningUnits.length\n                    });\n                    console.log(`Allocated ${totalDailyEarnings} mining earnings to ${allocations.length} units for user ${user.id}`);\n                }\n            } catch (userError) {\n                console.error(`Error processing mining earnings for user ${user.id}:`, userError);\n            }\n        }\n        // Log the daily ROI calculation\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'DAILY_ROI_CALCULATED',\n            details: {\n                usersProcessed: results.length,\n                totalEarnings: results.reduce((sum, r)=>sum + r.totalEarnings, 0),\n                totalAllocations: results.reduce((sum, r)=>sum + r.allocations.length, 0),\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Daily ROI calculation completed. Processed ${results.length} users with FIFO allocation.`);\n        return results;\n    } catch (error) {\n        console.error('Daily ROI calculation error:', error);\n        throw error;\n    }\n}\n// Process weekly earnings distribution (Saturday 15:00 UTC)\nasync function processWeeklyEarnings() {\n    try {\n        console.log('Starting weekly earnings distribution...');\n        // Get all pending mining earnings\n        const pendingEarnings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where: {\n                type: 'MINING_EARNINGS',\n                status: 'PENDING'\n            },\n            include: {\n                user: true\n            }\n        });\n        console.log(`Found ${pendingEarnings.length} pending earnings transactions`);\n        const userEarnings = new Map();\n        // Group earnings by user\n        for (const transaction of pendingEarnings){\n            const currentTotal = userEarnings.get(transaction.userId) || 0;\n            userEarnings.set(transaction.userId, currentTotal + transaction.amount);\n        }\n        const results = [];\n        // Process each user's earnings\n        for (const [userId, totalEarnings] of userEarnings){\n            try {\n                // Mark all pending transactions as completed\n                await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n                    where: {\n                        userId,\n                        type: 'MINING_EARNINGS',\n                        status: 'PENDING'\n                    },\n                    data: {\n                        status: 'COMPLETED'\n                    }\n                });\n                results.push({\n                    userId,\n                    totalEarnings\n                });\n            } catch (userError) {\n                console.error(`Error processing earnings for user ${userId}:`, userError);\n            }\n        }\n        // Log the weekly distribution\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'WEEKLY_EARNINGS_DISTRIBUTED',\n            details: {\n                usersProcessed: results.length,\n                totalDistributed: results.reduce((sum, r)=>sum + r.totalEarnings, 0),\n                transactionsProcessed: pendingEarnings.length,\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Weekly earnings distribution completed. Processed ${results.length} users.`);\n        return results;\n    } catch (error) {\n        console.error('Weekly earnings distribution error:', error);\n        throw error;\n    }\n}\n// Check and expire mining units that have reached 24 months\nasync function expireOldMiningUnits() {\n    try {\n        console.log('Checking for expired mining units...');\n        const expiredUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    lte: new Date()\n                }\n            }\n        });\n        console.log(`Found ${expiredUnits.length} units to expire`);\n        for (const unit of expiredUnits){\n            await _database__WEBPACK_IMPORTED_MODULE_1__.miningUnitDb.expireUnit(unit.id);\n            // Note: User active status is now computed dynamically\n            await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n                action: 'MINING_UNIT_EXPIRED',\n                userId: unit.userId,\n                details: {\n                    miningUnitId: unit.id,\n                    reason: '24_months_reached',\n                    totalEarned: unit.totalEarned,\n                    investmentAmount: unit.investmentAmount\n                }\n            });\n        }\n        return expiredUnits.length;\n    } catch (error) {\n        console.error('Mining unit expiry check error:', error);\n        throw error;\n    }\n}\n// Get mining statistics\nasync function getMiningStats() {\n    try {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction([\n            // Total TH/s sold\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                _sum: {\n                    thsAmount: true\n                }\n            }),\n            // Active TH/s\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                where: {\n                    status: 'ACTIVE'\n                },\n                _sum: {\n                    thsAmount: true\n                }\n            }),\n            // Total investment\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                _sum: {\n                    investmentAmount: true\n                }\n            }),\n            // Total earnings distributed\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.aggregate({\n                where: {\n                    type: 'MINING_EARNINGS',\n                    status: 'COMPLETED'\n                },\n                _sum: {\n                    amount: true\n                }\n            }),\n            // Active mining units count\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.count({\n                where: {\n                    status: 'ACTIVE'\n                }\n            }),\n            // Total mining units count\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.count()\n        ]);\n        return {\n            totalTHSSold: stats[0]._sum.thsAmount || 0,\n            activeTHS: stats[1]._sum.thsAmount || 0,\n            totalInvestment: stats[2]._sum.investmentAmount || 0,\n            totalEarningsDistributed: stats[3]._sum.amount || 0,\n            activeMiningUnits: stats[4],\n            totalMiningUnits: stats[5]\n        };\n    } catch (error) {\n        console.error('Mining stats error:', error);\n        throw error;\n    }\n}\n// Calculate user's estimated earnings\nasync function calculateEstimatedEarnings(userId) {\n    try {\n        const activeMiningUnits = await _database__WEBPACK_IMPORTED_MODULE_1__.miningUnitDb.findActiveByUserId(userId);\n        if (activeMiningUnits.length === 0) {\n            return {\n                next7Days: 0,\n                next30Days: 0,\n                next365Days: 0,\n                next2Years: 0\n            };\n        }\n        let totalDaily = 0;\n        for (const unit of activeMiningUnits){\n            const dailyEarnings = unit.investmentAmount * unit.dailyROI / 100;\n            const maxEarnings = unit.investmentAmount * 5;\n            const remainingEarnings = maxEarnings - unit.totalEarned;\n            // Use the lower of daily earnings or remaining earnings\n            totalDaily += Math.min(dailyEarnings, remainingEarnings);\n        }\n        return {\n            next7Days: totalDaily * 7,\n            next30Days: totalDaily * 30,\n            next365Days: totalDaily * 365,\n            next2Years: totalDaily * 730\n        };\n    } catch (error) {\n        console.error('Estimated earnings calculation error:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mining.ts\n");

/***/ })

};
;