'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON>le, CardContent, Button, Input, useMessageBox } from '@/components/ui';
import { Mail, Send, Settings, Shield, Eye, EyeOff, TestTube, FileText, Plus, Edit, Trash2 } from 'lucide-react';
import { EmailTemplateModal } from './EmailTemplateModal';

interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean;
  smtpUser: string;
  smtpPassword: string;
  fromName: string;
  fromEmail: string;
  emailEnabled: boolean;
}

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export const EmailSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'settings' | 'templates'>('settings');
  const [settings, setSettings] = useState<EmailSettings>({
    smtpHost: '',
    smtpPort: 587,
    smtpSecure: false,
    smtpUser: '',
    smtpPassword: '',
    fromName: 'HashCoreX',
    fromEmail: '',
    emailEnabled: true,
  });
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [seedingTemplates, setSeedingTemplates] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null);
  const [showTemplateModal, setShowTemplateModal] = useState(false);

  const { showMessage, MessageBoxComponent } = useMessageBox();

  useEffect(() => {
    fetchEmailSettings();
    if (activeTab === 'templates') {
      fetchEmailTemplates();
    }
  }, [activeTab]);

  const fetchEmailSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/email-settings', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Ensure all values are properly initialized to prevent uncontrolled input errors
          setSettings({
            smtpHost: data.data.smtpHost || '',
            smtpPort: data.data.smtpPort || 587,
            smtpSecure: Boolean(data.data.smtpSecure),
            smtpUser: data.data.smtpUser || '',
            smtpPassword: data.data.smtpPassword || '',
            fromName: data.data.fromName || 'HashCoreX',
            fromEmail: data.data.fromEmail || '',
            emailEnabled: Boolean(data.data.emailEnabled),
          });
        }
      }
    } catch (error) {
      console.error('Failed to fetch email settings:', error);
      showMessage({
        title: 'Error',
        message: 'Failed to load email settings',
        variant: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      const response = await fetch('/api/admin/email-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(settings),
      });

      const data = await response.json();

      if (data.success) {
        showMessage({
          title: 'Success',
          message: 'Email settings saved successfully',
          variant: 'success',
        });
      } else {
        throw new Error(data.error || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Failed to save email settings:', error);
      showMessage({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to save email settings',
        variant: 'error',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleTestEmail = async () => {
    if (!testEmail) {
      showMessage({
        title: 'Error',
        message: 'Please enter a test email address',
        variant: 'error',
      });
      return;
    }

    try {
      setTesting(true);
      
      const response = await fetch('/api/admin/email-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ testEmail }),
      });

      const data = await response.json();

      if (data.success) {
        showMessage({
          title: 'Success',
          message: 'Test email sent successfully! Check your inbox.',
          variant: 'success',
        });
      } else {
        throw new Error(data.error || 'Failed to send test email');
      }
    } catch (error) {
      console.error('Failed to send test email:', error);
      showMessage({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to send test email',
        variant: 'error',
      });
    } finally {
      setTesting(false);
    }
  };

  const handleInputChange = (field: keyof EmailSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Template management functions
  const fetchEmailTemplates = async () => {
    try {
      const response = await fetch('/api/admin/email-templates', {
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        setTemplates(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch templates');
      }
    } catch (error) {
      console.error('Failed to fetch email templates:', error);
      showMessage({
        title: 'Error',
        message: 'Failed to fetch email templates',
        variant: 'error',
      });
    }
  };

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    setShowTemplateModal(true);
  };

  const handleEditTemplate = (template: EmailTemplate) => {
    setEditingTemplate(template);
    setShowTemplateModal(true);
  };

  const handleDeleteTemplate = async (templateName: string) => {
    if (!confirm('Are you sure you want to delete this template?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/email-templates/${templateName}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        showMessage({
          title: 'Success',
          message: 'Template deleted successfully',
          variant: 'success',
        });
        fetchEmailTemplates();
      } else {
        throw new Error(data.error || 'Failed to delete template');
      }
    } catch (error) {
      console.error('Failed to delete template:', error);
      showMessage({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to delete template',
        variant: 'error',
      });
    }
  };

  const handleSeedTemplates = async () => {
    try {
      setSeedingTemplates(true);
      const response = await fetch('/api/admin/email-templates/seed', {
        method: 'POST',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        const createdCount = data.data.filter((result: any) => result.status === 'created').length;
        const existingCount = data.data.filter((result: any) => result.status === 'exists').length;

        showMessage({
          title: 'Success',
          message: `Templates seeded successfully! Created: ${createdCount}, Already existed: ${existingCount}`,
          variant: 'success',
        });
        fetchEmailTemplates();
      } else {
        throw new Error(data.error || 'Failed to seed templates');
      }
    } catch (error) {
      console.error('Failed to seed email templates:', error);
      showMessage({
        title: 'Error',
        message: 'Failed to seed email templates',
        variant: 'error',
      });
    } finally {
      setSeedingTemplates(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <MessageBoxComponent />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Email Settings</h1>
          <p className="text-slate-400 mt-1">Configure SMTP settings and email templates</p>
        </div>
        <div className="flex items-center space-x-3">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={settings.emailEnabled}
              onChange={(e) => handleInputChange('emailEnabled', e.target.checked)}
              className="rounded border-slate-600 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-slate-300">Email Enabled</span>
          </label>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-slate-800 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('settings')}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'settings'
              ? 'bg-blue-600 text-white'
              : 'text-slate-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          <Settings className="w-4 h-4 mr-2" />
          SMTP Settings
        </button>
        <button
          onClick={() => setActiveTab('templates')}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'templates'
              ? 'bg-blue-600 text-white'
              : 'text-slate-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          <FileText className="w-4 h-4 mr-2" />
          Email Templates
        </button>
      </div>

      {activeTab === 'settings' && (
        <>
      {/* SMTP Configuration */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Settings className="h-5 w-5" />
            SMTP Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                SMTP Host *
              </label>
              <Input
                type="text"
                value={settings.smtpHost}
                onChange={(e) => handleInputChange('smtpHost', e.target.value)}
                placeholder="smtp.gmail.com"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                SMTP Port
              </label>
              <Input
                type="number"
                value={settings.smtpPort}
                onChange={(e) => handleInputChange('smtpPort', parseInt(e.target.value))}
                placeholder="587"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                SMTP User *
              </label>
              <Input
                type="text"
                value={settings.smtpUser}
                onChange={(e) => handleInputChange('smtpUser', e.target.value)}
                placeholder="<EMAIL>"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                SMTP Password *
              </label>
              <div className="relative">
                <Input
                  type={showPassword ? 'text' : 'password'}
                  value={settings.smtpPassword}
                  onChange={(e) => handleInputChange('smtpPassword', e.target.value)}
                  placeholder="App password or SMTP password"
                  className="bg-slate-700 border-slate-600 text-white pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-slate-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-slate-400" />
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.smtpSecure}
                onChange={(e) => handleInputChange('smtpSecure', e.target.checked)}
                className="rounded border-slate-600 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-slate-300">Use SSL/TLS</span>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Email Configuration */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Mail className="h-5 w-5" />
            Email Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                From Name
              </label>
              <Input
                type="text"
                value={settings.fromName}
                onChange={(e) => handleInputChange('fromName', e.target.value)}
                placeholder="HashCoreX"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                From Email *
              </label>
              <Input
                type="email"
                value={settings.fromEmail}
                onChange={(e) => handleInputChange('fromEmail', e.target.value)}
                placeholder="<EMAIL>"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Email */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <TestTube className="h-5 w-5" />
            Test Email Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-end space-x-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Test Email Address
              </label>
              <Input
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <Button
              onClick={handleTestEmail}
              disabled={testing || !testEmail}
              variant="outline"
              className="border-blue-600 text-blue-400 hover:bg-blue-600 hover:text-white"
            >
              {testing ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              Send Test
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={saving}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {saving ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
          ) : (
            <Shield className="h-4 w-4 mr-2" />
          )}
          Save Settings
        </Button>
      </div>
        </>
      )}

      {activeTab === 'templates' && (
        <div className="space-y-6">
          {/* Templates Header */}
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-white">Email Templates</h2>
              <p className="text-slate-400 text-sm">Manage email templates with variables like {`{{firstName}}, {{otp}}, etc.`}</p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                onClick={handleSeedTemplates}
                variant="outline"
                className="border-blue-600 text-blue-400 hover:bg-blue-900/20"
                loading={seedingTemplates}
              >
                <FileText className="w-4 h-4 mr-2" />
                {seedingTemplates ? 'Seeding...' : 'Seed Default Templates'}
              </Button>
              <Button
                onClick={handleCreateTemplate}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Template
              </Button>
            </div>
          </div>

          {/* Templates List */}
          <div className="grid gap-4">
            {templates.map((template) => (
              <Card key={template.id} className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <h3 className="font-medium text-white">{template.name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          template.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {template.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <p className="text-slate-400 text-sm mt-1">{template.subject}</p>
                      <p className="text-slate-500 text-xs mt-1">
                        Updated: {new Date(template.updatedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() => handleEditTemplate(template)}
                        variant="outline"
                        size="sm"
                        className="border-slate-600 text-slate-300 hover:bg-slate-700"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        onClick={() => handleDeleteTemplate(template.name)}
                        variant="outline"
                        size="sm"
                        className="border-red-600 text-red-400 hover:bg-red-900/20"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {templates.length === 0 && (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-slate-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-400 mb-2">No templates found</h3>
                <p className="text-slate-500 mb-4">Create your first email template to get started.</p>
                <Button
                  onClick={handleCreateTemplate}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Template
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Template Modal */}
      <EmailTemplateModal
        isOpen={showTemplateModal}
        onClose={() => setShowTemplateModal(false)}
        template={editingTemplate}
        onSave={fetchEmailTemplates}
      />
    </div>
  );
};
