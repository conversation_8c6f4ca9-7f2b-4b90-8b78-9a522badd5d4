/**
 * Server-Side Scheduler Service
 * Replaces external cron jobs with internal Node.js scheduling
 * Maintains exact same timing and functionality as previous cron implementation
 */

import { calculateDailyROI, expireOldMiningUnits, processWeeklyEarnings } from './mining';
import { processBinaryMatching } from './referral';
import { systemLogDb } from './database';

interface ScheduledTask {
  name: string;
  interval: NodeJS.Timeout | null;
  isRunning: boolean;
  lastRun: Date | null;
  nextRun: Date | null;
  runCount: number;
}

class SchedulerService {
  private static instance: SchedulerService;
  private tasks: Map<string, ScheduledTask> = new Map();
  private isInitialized = false;

  private constructor() {}

  static getInstance(): SchedulerService {
    if (!SchedulerService.instance) {
      SchedulerService.instance = new SchedulerService();
    }
    return SchedulerService.instance;
  }

  /**
   * Initialize all scheduled tasks
   */
  async initialize() {
    if (this.isInitialized) {
      console.log('Scheduler service already initialized');
      return;
    }

    console.log('Initializing server-side scheduler service...');

    try {
      // Schedule daily ROI calculation (daily at 00:01 UTC)
      this.scheduleDailyROI();

      // Schedule weekly payout (Saturday at 15:00 UTC)
      this.scheduleWeeklyPayout();

      // Schedule binary matching (Saturday at 15:00 UTC)
      this.scheduleBinaryMatching();

      // Schedule deposit processing (every 10 minutes)
      this.scheduleDepositProcessing();

      this.isInitialized = true;
      console.log('Server-side scheduler service initialized successfully');

      // Log initialization
      await systemLogDb.create({
        action: 'SCHEDULER_SERVICE_INITIALIZED',
        details: {
          tasksScheduled: this.tasks.size,
          initTime: new Date().toISOString(),
          tasks: Array.from(this.tasks.keys()),
        },
      });

    } catch (error) {
      console.error('Error initializing scheduler service:', error);
      throw error;
    }
  }

  /**
   * Schedule daily ROI calculation (daily at 00:01 UTC)
   */
  private scheduleDailyROI() {
    const taskName = 'daily-roi';
    
    // Calculate time until next 00:01 UTC
    const now = new Date();
    const nextRun = new Date();
    nextRun.setUTCHours(0, 1, 0, 0); // 00:01 UTC
    
    // If we've passed today's 00:01 UTC, schedule for tomorrow
    if (nextRun <= now) {
      nextRun.setUTCDate(nextRun.getUTCDate() + 1);
    }

    const timeUntilNext = nextRun.getTime() - now.getTime();

    console.log(`Scheduling daily ROI calculation for ${nextRun.toISOString()}`);

    // Initial timeout to reach the first scheduled time
    setTimeout(() => {
      this.runDailyROI();
      
      // Then set up daily interval (24 hours = 86400000 ms)
      const interval = setInterval(() => {
        this.runDailyROI();
      }, 86400000);

      this.tasks.set(taskName, {
        name: taskName,
        interval,
        isRunning: false,
        lastRun: null,
        nextRun: new Date(Date.now() + 86400000),
        runCount: 0,
      });
    }, timeUntilNext);

    // Track the task
    this.tasks.set(taskName, {
      name: taskName,
      interval: null,
      isRunning: false,
      lastRun: null,
      nextRun,
      runCount: 0,
    });
  }

  /**
   * Schedule weekly payout (Saturday at 15:00 UTC)
   */
  private scheduleWeeklyPayout() {
    const taskName = 'weekly-payout';
    
    // Calculate time until next Saturday 15:00 UTC
    const nextRun = this.getNextWeeklyTime(6, 15); // Saturday (6), 15:00
    const timeUntilNext = nextRun.getTime() - Date.now();

    console.log(`Scheduling weekly payout for ${nextRun.toISOString()}`);

    // Initial timeout to reach the first scheduled time
    setTimeout(() => {
      this.runWeeklyPayout();
      
      // Then set up weekly interval (7 days = 604800000 ms)
      const interval = setInterval(() => {
        this.runWeeklyPayout();
      }, 604800000);

      this.tasks.set(taskName, {
        name: taskName,
        interval,
        isRunning: false,
        lastRun: null,
        nextRun: new Date(Date.now() + 604800000),
        runCount: 0,
      });
    }, timeUntilNext);

    // Track the task
    this.tasks.set(taskName, {
      name: taskName,
      interval: null,
      isRunning: false,
      lastRun: null,
      nextRun,
      runCount: 0,
    });
  }

  /**
   * Schedule binary matching (Saturday at 15:00 UTC)
   */
  private scheduleBinaryMatching() {
    const taskName = 'binary-matching';
    
    // Calculate time until next Saturday 15:00 UTC
    const nextRun = this.getNextWeeklyTime(6, 15); // Saturday (6), 15:00
    const timeUntilNext = nextRun.getTime() - Date.now();

    console.log(`Scheduling binary matching for ${nextRun.toISOString()}`);

    // Initial timeout to reach the first scheduled time
    setTimeout(() => {
      this.runBinaryMatching();
      
      // Then set up weekly interval (7 days = 604800000 ms)
      const interval = setInterval(() => {
        this.runBinaryMatching();
      }, 604800000);

      this.tasks.set(taskName, {
        name: taskName,
        interval,
        isRunning: false,
        lastRun: null,
        nextRun: new Date(Date.now() + 604800000),
        runCount: 0,
      });
    }, timeUntilNext);

    // Track the task
    this.tasks.set(taskName, {
      name: taskName,
      interval: null,
      isRunning: false,
      lastRun: null,
      nextRun,
      runCount: 0,
    });
  }

  /**
   * Schedule deposit processing (every 10 minutes)
   */
  private scheduleDepositProcessing() {
    const taskName = 'deposit-processing';
    
    console.log('Scheduling deposit processing every 10 minutes');

    // Start immediately, then every 10 minutes (600000 ms)
    const interval = setInterval(() => {
      this.runDepositProcessing();
    }, 600000);

    // Also run immediately
    this.runDepositProcessing();

    this.tasks.set(taskName, {
      name: taskName,
      interval,
      isRunning: false,
      lastRun: null,
      nextRun: new Date(Date.now() + 600000),
      runCount: 0,
    });
  }

  /**
   * Calculate next weekly time (e.g., Saturday 15:00 UTC)
   */
  private getNextWeeklyTime(dayOfWeek: number, hour: number): Date {
    const now = new Date();
    const nextRun = new Date();
    
    // Set to the target day and hour
    nextRun.setUTCHours(hour, 0, 0, 0);
    
    // Calculate days until target day
    const currentDay = now.getUTCDay();
    let daysUntilTarget = dayOfWeek - currentDay;
    
    // If target day has passed this week, or it's today but past the hour
    if (daysUntilTarget < 0 || (daysUntilTarget === 0 && now.getUTCHours() >= hour)) {
      daysUntilTarget += 7;
    }
    
    nextRun.setUTCDate(now.getUTCDate() + daysUntilTarget);
    
    return nextRun;
  }

  /**
   * Execute daily ROI calculation
   */
  private async runDailyROI() {
    const taskName = 'daily-roi';
    const task = this.tasks.get(taskName);
    
    if (!task || task.isRunning) {
      console.log(`Daily ROI task already running or not found`);
      return;
    }

    task.isRunning = true;
    task.lastRun = new Date();
    task.runCount++;

    try {
      console.log('Starting scheduled daily ROI calculation...');

      // First, expire any old mining units
      const expiredCount = await expireOldMiningUnits();
      console.log(`Expired ${expiredCount} old mining units`);

      // Calculate daily ROI for all active units
      const roiResults = await calculateDailyROI();
      console.log(`Processed ${roiResults.length} mining units for daily ROI`);

      const totalEarnings = roiResults.reduce((sum, result) => sum + result.earnings, 0);
      const expiredUnits = roiResults.filter(result => result.expired).length;

      // Log the execution
      await systemLogDb.create({
        action: 'DAILY_ROI_SCHEDULED_EXECUTED',
        details: {
          unitsProcessed: roiResults.length,
          totalEarnings,
          expiredUnits,
          oldUnitsExpired: expiredCount,
          executionTime: new Date().toISOString(),
          runCount: task.runCount,
        },
      });

      console.log(`Daily ROI calculation completed successfully (run #${task.runCount})`);

    } catch (error) {
      console.error('Daily ROI scheduled task error:', error);

      await systemLogDb.create({
        action: 'DAILY_ROI_SCHEDULED_ERROR',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          timestamp: new Date().toISOString(),
          runCount: task.runCount,
        },
      });
    } finally {
      task.isRunning = false;
      task.nextRun = new Date(Date.now() + 86400000); // Next day
    }
  }

  /**
   * Execute weekly payout
   */
  private async runWeeklyPayout() {
    const taskName = 'weekly-payout';
    const task = this.tasks.get(taskName);

    if (!task || task.isRunning) {
      console.log(`Weekly payout task already running or not found`);
      return;
    }

    task.isRunning = true;
    task.lastRun = new Date();
    task.runCount++;

    try {
      console.log('Starting scheduled weekly earnings payout...');

      // Process weekly earnings distribution
      const payoutResults = await processWeeklyEarnings();
      console.log(`Processed earnings for ${payoutResults.length} users`);

      const totalDistributed = payoutResults.reduce((sum, result) => sum + result.totalEarnings, 0);

      // Log the execution
      await systemLogDb.create({
        action: 'WEEKLY_PAYOUT_SCHEDULED_EXECUTED',
        details: {
          usersProcessed: payoutResults.length,
          totalDistributed,
          executionTime: new Date().toISOString(),
          payoutDay: 'Saturday',
          payoutTime: '15:00 UTC',
          runCount: task.runCount,
        },
      });

      console.log(`Weekly payout completed successfully (run #${task.runCount})`);

    } catch (error) {
      console.error('Weekly payout scheduled task error:', error);

      await systemLogDb.create({
        action: 'WEEKLY_PAYOUT_SCHEDULED_ERROR',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          timestamp: new Date().toISOString(),
          runCount: task.runCount,
        },
      });
    } finally {
      task.isRunning = false;
      task.nextRun = new Date(Date.now() + 604800000); // Next week
    }
  }

  /**
   * Execute binary matching
   */
  private async runBinaryMatching() {
    const taskName = 'binary-matching';
    const task = this.tasks.get(taskName);

    if (!task || task.isRunning) {
      console.log(`Binary matching task already running or not found`);
      return;
    }

    task.isRunning = true;
    task.lastRun = new Date();
    task.runCount++;

    try {
      console.log('Starting scheduled binary matching...');

      // Process binary matching
      const matchingResults = await processBinaryMatching();

      const totalPayouts = matchingResults.reduce((sum, result) => sum + result.payout, 0);
      const totalMatchedPoints = matchingResults.reduce((sum, result) => sum + result.matchedPoints, 0);

      // Log the execution
      await systemLogDb.create({
        action: 'BINARY_MATCHING_SCHEDULED_EXECUTED',
        details: {
          usersProcessed: matchingResults.length,
          totalPayouts,
          totalMatchedPoints,
          executionTime: new Date().toISOString(),
          matchingTime: '15:00 UTC (Weekly)',
          runCount: task.runCount,
        },
      });

      console.log(`Binary matching completed successfully (run #${task.runCount})`);

    } catch (error) {
      console.error('Binary matching scheduled task error:', error);

      await systemLogDb.create({
        action: 'BINARY_MATCHING_SCHEDULED_ERROR',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          timestamp: new Date().toISOString(),
          runCount: task.runCount,
        },
      });
    } finally {
      task.isRunning = false;
      task.nextRun = new Date(Date.now() + 604800000); // Next week
    }
  }

  /**
   * Execute deposit processing
   */
  private async runDepositProcessing() {
    const taskName = 'deposit-processing';
    const task = this.tasks.get(taskName);

    if (!task || task.isRunning) {
      console.log(`Deposit processing task already running or not found`);
      return;
    }

    task.isRunning = true;
    task.lastRun = new Date();
    task.runCount++;

    try {
      console.log('Starting scheduled deposit processing...');

      // Import the deposit processing logic from the cron route
      const { processDeposits } = await import('./depositProcessing');
      const result = await processDeposits();

      // Log the execution
      await systemLogDb.create({
        action: 'DEPOSIT_PROCESSING_SCHEDULED_EXECUTED',
        details: {
          ...result,
          executionTime: new Date().toISOString(),
          runCount: task.runCount,
        },
      });

      console.log(`Deposit processing completed successfully (run #${task.runCount})`);

    } catch (error) {
      console.error('Deposit processing scheduled task error:', error);

      await systemLogDb.create({
        action: 'DEPOSIT_PROCESSING_SCHEDULED_ERROR',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          timestamp: new Date().toISOString(),
          runCount: task.runCount,
        },
      });
    } finally {
      task.isRunning = false;
      task.nextRun = new Date(Date.now() + 600000); // Next 10 minutes
    }
  }

  /**
   * Stop all scheduled tasks
   */
  stop() {
    console.log('Stopping scheduler service...');

    for (const [taskName, task] of this.tasks) {
      if (task.interval) {
        clearInterval(task.interval);
        console.log(`Stopped task: ${taskName}`);
      }
    }

    this.tasks.clear();
    this.isInitialized = false;
    console.log('Scheduler service stopped');
  }

  /**
   * Get status of all scheduled tasks
   */
  getStatus() {
    const status = {
      isInitialized: this.isInitialized,
      totalTasks: this.tasks.size,
      tasks: Array.from(this.tasks.entries()).map(([name, task]) => ({
        name,
        isRunning: task.isRunning,
        lastRun: task.lastRun?.toISOString() || null,
        nextRun: task.nextRun?.toISOString() || null,
        runCount: task.runCount,
      })),
    };

    return status;
  }

  /**
   * Manually trigger a specific task (for testing/admin purposes)
   */
  async triggerTask(taskName: string) {
    switch (taskName) {
      case 'daily-roi':
        await this.runDailyROI();
        break;
      case 'weekly-payout':
        await this.runWeeklyPayout();
        break;
      case 'binary-matching':
        await this.runBinaryMatching();
        break;
      case 'deposit-processing':
        await this.runDepositProcessing();
        break;
      default:
        throw new Error(`Unknown task: ${taskName}`);
    }
  }
}

// Export singleton instance
export const schedulerService = SchedulerService.getInstance();
