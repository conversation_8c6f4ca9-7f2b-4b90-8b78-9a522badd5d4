import { emailService } from './email';
import { emailLogDb, userDb } from './database';

export interface EmailNotificationData {
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
}

export interface DepositNotificationData extends EmailNotificationData {
  amount: number;
  transactionId: string;
  currency: string;
}

export interface KYCNotificationData extends EmailNotificationData {
  status: 'APPROVED' | 'REJECTED';
  rejectionReason?: string;
}

export interface WithdrawalNotificationData extends EmailNotificationData {
  amount: number;
  status: 'APPROVED' | 'REJECTED' | 'COMPLETED' | 'FAILED';
  transactionHash?: string;
  rejectionReason?: string;
  usdtAddress?: string;
}

class EmailNotificationService {
  /**
   * Send deposit success notification
   */
  async sendDepositSuccessNotification(data: DepositNotificationData): Promise<boolean> {
    try {
      const template = await emailService.getEmailTemplate('deposit_success');

      if (!template) {
        console.warn('Email template "deposit_success" not found. Using default template.');
      }

      const subject = template?.subject || 'Deposit Confirmed - HashCoreX';
      let html = template?.htmlContent || this.getDefaultDepositSuccessTemplate();
      let text = template?.textContent || '';

      // Replace variables
      html = this.replaceVariables(html, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        amount: data.amount.toString(),
        transactionId: data.transactionId,
        currency: data.currency,
      });

      text = this.replaceVariables(text, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        amount: data.amount.toString(),
        transactionId: data.transactionId,
        currency: data.currency,
      });

      // Create email log entry
      const emailLog = await emailLogDb.create({
        to: data.email,
        subject,
        template: 'deposit_success',
        status: 'PENDING',
      });

      const emailSent = await emailService.sendEmail({
        to: data.email,
        subject,
        html,
        text: text || `Deposit Confirmed\n\nAmount: ${data.amount} ${data.currency}\nTransaction ID: ${data.transactionId}`,
      });

      if (emailSent) {
        await emailLogDb.updateStatus(emailLog.id, 'SENT');
        return true;
      } else {
        await emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');
        return false;
      }
    } catch (error) {
      console.error('Error sending deposit success notification:', error);
      return false;
    }
  }

  /**
   * Send KYC status notification
   */
  async sendKYCStatusNotification(data: KYCNotificationData): Promise<boolean> {
    try {
      const templateName = data.status === 'APPROVED' ? 'kyc_approved' : 'kyc_rejected';
      const template = await emailService.getEmailTemplate(templateName);

      if (!template) {
        console.warn(`Email template "${templateName}" not found. Using default template.`);
      }

      const subject = template?.subject || `KYC ${data.status === 'APPROVED' ? 'Approved' : 'Rejected'} - HashCoreX`;
      let html = template?.htmlContent || this.getDefaultKYCTemplate(data.status);
      let text = template?.textContent || '';

      // Replace variables
      html = this.replaceVariables(html, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        status: data.status,
        rejectionReason: data.rejectionReason || '',
      });

      text = this.replaceVariables(text, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        status: data.status,
        rejectionReason: data.rejectionReason || '',
      });

      // Create email log entry
      const emailLog = await emailLogDb.create({
        to: data.email,
        subject,
        template: templateName,
        status: 'PENDING',
      });

      const emailSent = await emailService.sendEmail({
        to: data.email,
        subject,
        html,
        text: text || `KYC ${data.status}\n\n${data.rejectionReason ? `Reason: ${data.rejectionReason}` : 'Your KYC has been approved.'}`,
      });

      if (emailSent) {
        await emailLogDb.updateStatus(emailLog.id, 'SENT');
        return true;
      } else {
        await emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');
        return false;
      }
    } catch (error) {
      console.error('Error sending KYC status notification:', error);
      return false;
    }
  }

  /**
   * Send withdrawal status notification
   */
  async sendWithdrawalStatusNotification(data: WithdrawalNotificationData): Promise<boolean> {
    try {
      const templateName = this.getWithdrawalTemplateName(data.status);
      const template = await emailService.getEmailTemplate(templateName);

      if (!template) {
        console.warn(`Email template "${templateName}" not found. Using default template.`);
      }

      const subject = template?.subject || `Withdrawal ${this.getWithdrawalStatusText(data.status)} - HashCoreX`;
      let html = template?.htmlContent || this.getDefaultWithdrawalTemplate(data.status);
      let text = template?.textContent || '';

      // Replace variables
      html = this.replaceVariables(html, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        amount: data.amount.toString(),
        status: data.status,
        transactionHash: data.transactionHash || '',
        rejectionReason: data.rejectionReason || '',
        usdtAddress: data.usdtAddress || '',
      });

      text = this.replaceVariables(text, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        amount: data.amount.toString(),
        status: data.status,
        transactionHash: data.transactionHash || '',
        rejectionReason: data.rejectionReason || '',
        usdtAddress: data.usdtAddress || '',
      });

      // Create email log entry
      const emailLog = await emailLogDb.create({
        to: data.email,
        subject,
        template: templateName,
        status: 'PENDING',
      });

      const emailSent = await emailService.sendEmail({
        to: data.email,
        subject,
        html,
        text: text || this.getDefaultWithdrawalText(data),
      });

      if (emailSent) {
        await emailLogDb.updateStatus(emailLog.id, 'SENT');
        return true;
      } else {
        await emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');
        return false;
      }
    } catch (error) {
      console.error('Error sending withdrawal status notification:', error);
      return false;
    }
  }

  /**
   * Replace template variables
   */
  private replaceVariables(template: string, variables: Record<string, string>): string {
    let result = template;
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    }
    return result;
  }

  /**
   * Get withdrawal template name based on status
   */
  private getWithdrawalTemplateName(status: string): string {
    switch (status) {
      case 'APPROVED':
        return 'withdrawal_approved';
      case 'REJECTED':
        return 'withdrawal_rejected';
      case 'COMPLETED':
        return 'withdrawal_completed';
      case 'FAILED':
        return 'withdrawal_failed';
      default:
        return 'withdrawal_status';
    }
  }

  /**
   * Get withdrawal status text
   */
  private getWithdrawalStatusText(status: string): string {
    switch (status) {
      case 'APPROVED':
        return 'Approved';
      case 'REJECTED':
        return 'Rejected';
      case 'COMPLETED':
        return 'Completed';
      case 'FAILED':
        return 'Failed';
      default:
        return 'Updated';
    }
  }

  /**
   * Default deposit success template
   */
  private getDefaultDepositSuccessTemplate(): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Deposit Confirmed!</h2>
          <p>Hello {{firstName}},</p>
          <p>Great news! Your deposit has been successfully confirmed and credited to your account.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin: 0 0 10px 0; color: #10b981;">Deposit Details</h3>
            <p><strong>Amount:</strong> {{amount}} {{currency}}</p>
            <p><strong>Transaction ID:</strong> {{transactionId}}</p>
            <p><strong>Status:</strong> Confirmed</p>
          </div>
          <p>Your funds are now available in your wallet and you can start using them immediately.</p>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `;
  }

  /**
   * Default KYC template
   */
  private getDefaultKYCTemplate(status: 'APPROVED' | 'REJECTED'): string {
    const isApproved = status === 'APPROVED';
    const color = isApproved ? '#10b981' : '#ef4444';
    const title = isApproved ? 'KYC Approved!' : 'KYC Rejected';
    const message = isApproved 
      ? 'Congratulations! Your KYC verification has been approved. You now have full access to all platform features.'
      : 'Unfortunately, your KYC verification has been rejected. Please review the reason below and resubmit with correct documents.';

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, ${color} 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">${title}</h2>
          <p>Hello {{firstName}},</p>
          <p>${message}</p>
          ${!isApproved ? '<div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;"><p><strong>Rejection Reason:</strong> {{rejectionReason}}</p></div>' : ''}
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `;
  }

  /**
   * Default withdrawal template
   */
  private getDefaultWithdrawalTemplate(status: string): string {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'APPROVED': return '#10b981';
        case 'COMPLETED': return '#10b981';
        case 'REJECTED': return '#ef4444';
        case 'FAILED': return '#ef4444';
        default: return '#6b7280';
      }
    };

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, ${getStatusColor(status)} 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Withdrawal {{status}}</h2>
          <p>Hello {{firstName}},</p>
          <p>Your withdrawal request has been {{status}}.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid ${getStatusColor(status)};">
            <h3 style="margin: 0 0 10px 0; color: ${getStatusColor(status)};">Withdrawal Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Status:</strong> {{status}}</p>
            {{#if usdtAddress}}<p><strong>Address:</strong> {{usdtAddress}}</p>{{/if}}
            {{#if transactionHash}}<p><strong>Transaction Hash:</strong> {{transactionHash}}</p>{{/if}}
            {{#if rejectionReason}}<p><strong>Reason:</strong> {{rejectionReason}}</p>{{/if}}
          </div>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `;
  }

  /**
   * Default withdrawal text
   */
  private getDefaultWithdrawalText(data: WithdrawalNotificationData): string {
    let text = `Withdrawal ${data.status}\n\nAmount: ${data.amount} USDT\nStatus: ${data.status}`;
    if (data.usdtAddress) text += `\nAddress: ${data.usdtAddress}`;
    if (data.transactionHash) text += `\nTransaction Hash: ${data.transactionHash}`;
    if (data.rejectionReason) text += `\nReason: ${data.rejectionReason}`;
    return text;
  }
}

export const emailNotificationService = new EmailNotificationService();
