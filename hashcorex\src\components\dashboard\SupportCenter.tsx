'use client';

import React, { useState, useEffect } from 'react';
import { Button, Input, Card, CardHeader, CardTitle, CardContent, Modal } from '@/components/ui';
import { Grid } from '@/components/layout';
import {
  MessageCircle,
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  Send,
  Bell,
  Settings,
  Mail,
  Smartphone,
  HelpCircle,
  BookOpen,
  CreditCard,
  Users,
  Shield,
  TrendingUp,
  Wallet
} from 'lucide-react';
import { formatDateTime } from '@/lib/utils';

interface SupportTicket {
  id: string;
  subject: string;
  message: string;
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  createdAt: string;
  updatedAt: string;
  responses: Array<{
    id: string;
    message: string;
    isAdmin: boolean;
    createdAt: string;
  }>;
}

interface FAQItem {
  id: string;
  category: string;
  question: string;
  answer: string;
  icon: React.ComponentType<any>;
  tags: string[];
}



// FAQ Data
const faqData: FAQItem[] = [
  {
    id: '1',
    category: 'Getting Started',
    question: 'How do I start mining with HashCoreX?',
    answer: `To start mining with HashCoreX, follow these simple steps:

1. **Complete KYC Verification**: First, complete your KYC verification in the Profile section. This is required for all mining activities.

2. **Deposit Funds**: Go to your Wallet and deposit USDT (TRC20) to fund your account. The minimum deposit amount is configured by the admin.

3. **Purchase Mining Units**: Navigate to the Mining section and purchase mining units. Choose your desired TH/s amount and investment level.

4. **Start Earning**: Your mining units will start generating daily returns automatically. Earnings are calculated based on your TH/s amount and current market conditions.

5. **Track Progress**: Monitor your earnings in the Earnings section and watch your mining units progress toward their 5x investment return limit.

Remember: Mining units expire after 24 months or when they reach 5x their investment amount, whichever comes first.`,
    icon: TrendingUp,
    tags: ['mining', 'getting started', 'kyc', 'deposit']
  },
  {
    id: '2',
    category: 'Mining Units',
    question: 'How are daily returns calculated?',
    answer: `Daily returns are calculated using a dynamic system based on your mining unit's TH/s amount:

**Dynamic ROI System:**
- Different TH/s ranges have different return rates
- Returns are calculated as: (Investment Amount × Daily ROI%) ÷ 100
- ROI percentages are determined by admin-configured ranges

**Example Ranges:**
- 0-10 TH/s: 0.3% - 0.5% daily
- 10-50 TH/s: 0.4% - 0.6% daily
- 50+ TH/s: 0.5% - 0.7% daily

**Important Notes:**
- Returns are credited daily but paid out weekly (Sundays at 00:00 AM GMT+5:30)
- Mining units expire when they reach 5x their investment amount
- FIFO system: Oldest units receive earnings first
- All earnings are subject to market conditions and admin configuration`,
    icon: TrendingUp,
    tags: ['mining', 'returns', 'calculation', 'roi']
  },
  {
    id: '3',
    category: 'Wallet & Payments',
    question: 'How do deposits and withdrawals work?',
    answer: `**Deposits:**
- Only USDT (TRC20) deposits are accepted
- Minimum deposit amount is set by admin (typically $50)
- Deposits are automatically verified on the Tron blockchain
- Funds are available immediately after confirmation

**Withdrawals:**
- Minimum withdrawal: $50
- Fixed fee: $3 + 1% of withdrawal amount
- Processing time: Up to 3 business days
- Only to verified TRC20 addresses

**Wallet Balance:**
- Available Balance: Funds ready for use or withdrawal
- Pending Balance: Earnings waiting for weekly distribution
- Total Earnings: Cumulative earnings from all sources

**Important:**
- Complete KYC verification before making withdrawals
- Ensure your TRC20 address is correct before submitting
- Withdrawal fees are deducted from your balance`,
    icon: Wallet,
    tags: ['wallet', 'deposit', 'withdrawal', 'usdt', 'trc20']
  },
  {
    id: '4',
    category: 'Referral System',
    question: 'How does the binary referral system work?',
    answer: `HashCoreX uses a binary tree referral system with three placement types:

**Placement Types:**
1. **General Referral**: Placed in weaker leg automatically
2. **Left-Side Referral**: Specifically placed on left side
3. **Right-Side Referral**: Specifically placed on right side

**Earning Structure:**
- **Direct Referral Commission**: 10% one-time bonus when your referral purchases mining units
- **Binary Matching Bonus**: Weekly matching of binary points (1 point = $10)

**Binary Points:**
- Earned when your referrals purchase mining units
- $150 purchase = 1.5 points (supports 2 decimal places)
- Points are matched weekly between left and right sides
- Excess points reset to 0 after matching

**Requirements:**
- You must have active mining units to earn commissions
- Binary matching occurs weekly at 15:00 UTC
- All earnings are allocated to your mining units using FIFO system`,
    icon: Users,
    tags: ['referral', 'binary', 'commission', 'points', 'matching']
  },
  {
    id: '5',
    category: 'Account & Security',
    question: 'What is KYC and why is it required?',
    answer: `**KYC (Know Your Customer) Verification:**

KYC is a mandatory verification process required for all HashCoreX users to ensure compliance with financial regulations.

**KYC Process:**
1. **Personal Information**: Provide accurate personal details
2. **ID Document**: Upload government-issued ID (passport, driver's license, etc.)
3. **Selfie Verification**: Take a selfie holding your ID document
4. **Admin Review**: Our team reviews your submission (typically 24-48 hours)

**KYC Status:**
- **Not Submitted**: KYC documents not yet uploaded
- **Pending**: Under admin review
- **Approved**: Verification complete - full access granted
- **Rejected**: Resubmission required with correct documents

**Why KYC is Required:**
- Legal compliance with financial regulations
- Account security and fraud prevention
- Enables withdrawals and full platform access
- Protects both users and the platform

**Important:** Profile name fields become read-only after KYC submission to prevent fraud.`,
    icon: Shield,
    tags: ['kyc', 'verification', 'security', 'compliance', 'identity']
  },
  {
    id: '6',
    category: 'Technical Support',
    question: 'What should I do if I encounter technical issues?',
    answer: `**Common Solutions:**

**Login Issues:**
- Clear browser cache and cookies
- Try incognito/private browsing mode
- Ensure you're using the correct email address
- Check if your account is active

**Transaction Issues:**
- Verify transaction hash on Tron blockchain explorer
- Check if you're using the correct network (Mainnet/Testnet)
- Ensure sufficient balance for fees
- Wait for blockchain confirmations

**Display Issues:**
- Refresh the page
- Try a different browser
- Disable browser extensions temporarily
- Check your internet connection

**Still Need Help?**
1. Create a support ticket with detailed information
2. Include screenshots if possible
3. Provide transaction hashes for payment issues
4. Specify your browser and device type

**Response Times:**
- General inquiries: 24-48 hours
- Technical issues: 12-24 hours
- Payment issues: Priority handling within 12 hours`,
    icon: Settings,
    tags: ['technical', 'support', 'troubleshooting', 'login', 'transactions']
  }
];

export const SupportCenter: React.FC = () => {
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewTicketModal, setShowNewTicketModal] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [selectedFAQ, setSelectedFAQ] = useState<FAQItem | null>(null);
  const [faqSearchTerm, setFaqSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [newTicketForm, setNewTicketForm] = useState({
    subject: '',
    message: '',
    priority: 'MEDIUM' as const,
  });
  const [newResponse, setNewResponse] = useState('');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchTickets();
  }, []);

  // Get unique categories for filtering
  const categories = ['All', ...Array.from(new Set(faqData.map(faq => faq.category)))];

  // Filter FAQs based on search term and category
  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(faqSearchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(faqSearchTerm.toLowerCase()) ||
                         faq.tags.some(tag => tag.toLowerCase().includes(faqSearchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const fetchTickets = async () => {
    try {
      const response = await fetch('/api/support/tickets', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTickets(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch tickets:', error);
    } finally {
      setLoading(false);
    }
  };



  const createTicket = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const response = await fetch('/api/support/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(newTicketForm),
      });

      if (response.ok) {
        setNewTicketForm({ subject: '', message: '', priority: 'MEDIUM' });
        setShowNewTicketModal(false);
        fetchTickets();
      }
    } catch (error) {
      console.error('Failed to create ticket:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const addResponse = async (ticketId: string) => {
    if (!newResponse.trim()) return;

    try {
      const response = await fetch(`/api/support/tickets/${ticketId}/responses`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ message: newResponse }),
      });

      if (response.ok) {
        setNewResponse('');
        fetchTickets();
        // Update selected ticket
        const updatedTickets = await fetch('/api/support/tickets', {
          credentials: 'include',
        });
        if (updatedTickets.ok) {
          const data = await updatedTickets.json();
          const updatedTicket = data.data.find((t: SupportTicket) => t.id === ticketId);
          if (updatedTicket) {
            setSelectedTicket(updatedTicket);
          }
        }
      }
    } catch (error) {
      console.error('Failed to add response:', error);
    }
  };



  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OPEN':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'IN_PROGRESS':
        return <Clock className="h-4 w-4 text-solar-500" />;
      case 'RESOLVED':
      case 'CLOSED':
        return <CheckCircle className="h-4 w-4 text-eco-500" />;
      default:
        return <MessageCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'bg-red-100 text-red-700';
      case 'IN_PROGRESS':
        return 'bg-solar-100 text-solar-700';
      case 'RESOLVED':
      case 'CLOSED':
        return 'bg-eco-100 text-eco-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'bg-red-100 text-red-700';
      case 'HIGH':
        return 'bg-orange-100 text-orange-700';
      case 'MEDIUM':
        return 'bg-solar-100 text-solar-700';
      case 'LOW':
        return 'bg-gray-100 text-gray-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Support Center</h2>
          <p className="text-gray-600 mt-1">Get help and manage support tickets</p>
        </div>
        <Button
          onClick={() => setShowNewTicketModal(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          New Ticket
        </Button>
      </div>

      {/* Support Tickets */}
      <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                Support Tickets
              </CardTitle>
            </CardHeader>
            <CardContent>
              {tickets.length > 0 ? (
                <div className="space-y-3">
                  {tickets.map((ticket) => (
                    <div
                      key={ticket.id}
                      onClick={() => setSelectedTicket(ticket)}
                      className="p-4 border border-gray-200 rounded-lg cursor-pointer"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-gray-900 truncate flex-1">
                          {ticket.subject}
                        </h4>
                        <div className="flex items-center gap-2 ml-2">
                          {getStatusIcon(ticket.status)}
                          <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(ticket.status)}`}>
                            {ticket.status}
                          </span>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                        {ticket.message}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span className={`px-2 py-1 rounded-full ${getPriorityColor(ticket.priority)}`}>
                          {ticket.priority}
                        </span>
                        <span>{formatDateTime(ticket.createdAt)}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Support Tickets</h3>
                  <p className="text-gray-600">You haven&apos;t created any support tickets yet.</p>
                </div>
              )}
            </CardContent>
          </Card>
      </div>

      {/* Help Tutorials & FAQ Section */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5" />
              Help Tutorials & FAQ
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Search and Filter */}
            <div className="mb-6 space-y-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search FAQs..."
                    value={faqSearchTerm}
                    onChange={(e) => setFaqSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className="sm:w-48">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* FAQ Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredFAQs.map((faq) => {
                const IconComponent = faq.icon;
                return (
                  <div
                    key={faq.id}
                    onClick={() => setSelectedFAQ(faq)}
                    className="p-6 border border-gray-200 rounded-lg cursor-pointer hover:border-solar-300 hover:shadow-md transition-all duration-200 bg-white"
                  >
                    <div className="flex items-start gap-3 mb-3">
                      <div className="p-2 bg-solar-100 rounded-lg">
                        <IconComponent className="h-5 w-5 text-solar-600" />
                      </div>
                      <div className="flex-1">
                        <span className="text-xs font-medium text-solar-600 bg-solar-50 px-2 py-1 rounded-full">
                          {faq.category}
                        </span>
                      </div>
                    </div>
                    <h4 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                      {faq.question}
                    </h4>
                    <p className="text-sm text-gray-600 line-clamp-3 mb-3">
                      {faq.answer.split('\n')[0]}
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {faq.tags.slice(0, 3).map(tag => (
                        <span key={tag} className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {tag}
                        </span>
                      ))}
                      {faq.tags.length > 3 && (
                        <span className="text-xs text-gray-500">+{faq.tags.length - 3} more</span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {filteredFAQs.length === 0 && (
              <div className="text-center py-12">
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No FAQs Found</h3>
                <p className="text-gray-600">
                  {faqSearchTerm || selectedCategory !== 'All'
                    ? 'Try adjusting your search or filter criteria.'
                    : 'FAQ content is being updated.'}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* New Ticket Modal */}
      <Modal
        isOpen={showNewTicketModal}
        onClose={() => setShowNewTicketModal(false)}
        title="Create Support Ticket"
      >
        <form onSubmit={createTicket} className="space-y-4">
          <Input
            label="Subject"
            value={newTicketForm.subject}
            onChange={(e) => setNewTicketForm(prev => ({ ...prev, subject: e.target.value }))}
            placeholder="Brief description of your issue"
            required
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Priority
            </label>
            <select
              value={newTicketForm.priority}
              onChange={(e) => setNewTicketForm(prev => ({ ...prev, priority: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"
            >
              <option value="LOW">Low</option>
              <option value="MEDIUM">Medium</option>
              <option value="HIGH">High</option>
              <option value="URGENT">Urgent</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Message
            </label>
            <textarea
              value={newTicketForm.message}
              onChange={(e) => setNewTicketForm(prev => ({ ...prev, message: e.target.value }))}
              placeholder="Describe your issue in detail..."
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"
              required
            />
          </div>

          <div className="flex space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowNewTicketModal(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              loading={submitting}
              className="flex-1"
            >
              Create Ticket
            </Button>
          </div>
        </form>
      </Modal>

      {/* Ticket Detail Modal */}
      {selectedTicket && (
        <Modal
          isOpen={!!selectedTicket}
          onClose={() => setSelectedTicket(null)}
          title={`Ticket: ${selectedTicket.subject}`}
          size="xl"
        >
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              {getStatusIcon(selectedTicket.status)}
              <span className={`text-sm px-2 py-1 rounded-full ${getStatusColor(selectedTicket.status)}`}>
                {selectedTicket.status}
              </span>
              <span className={`text-sm px-2 py-1 rounded-full ${getPriorityColor(selectedTicket.priority)}`}>
                {selectedTicket.priority}
              </span>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600 mb-2">Original Message:</p>
              <p className="text-gray-900">{selectedTicket.message}</p>
              <p className="text-xs text-gray-500 mt-2">
                Created: {formatDateTime(selectedTicket.createdAt)}
              </p>
            </div>

            {selectedTicket.responses.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Responses:</h4>
                {selectedTicket.responses.map((response) => (
                  <div
                    key={response.id}
                    className={`p-3 rounded-lg ${
                      response.isAdmin ? 'bg-blue-50 border-l-4 border-blue-500' : 'bg-gray-50'
                    }`}
                  >
                    <p className="text-gray-900">{response.message}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {response.isAdmin ? 'Support Team' : 'You'} • {formatDateTime(response.createdAt)}
                    </p>
                  </div>
                ))}
              </div>
            )}

            {selectedTicket.status !== 'CLOSED' && (
              <div className="space-y-3">
                <textarea
                  value={newResponse}
                  onChange={(e) => setNewResponse(e.target.value)}
                  placeholder="Add a response..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"
                />
                <Button
                  onClick={() => addResponse(selectedTicket.id)}
                  disabled={!newResponse.trim()}
                  className="w-full"
                >
                  <Send className="h-4 w-4 mr-2" />
                  Send Response
                </Button>
              </div>
            )}
          </div>
        </Modal>
      )}

      {/* FAQ Detail Modal */}
      {selectedFAQ && (
        <Modal
          isOpen={!!selectedFAQ}
          onClose={() => setSelectedFAQ(null)}
          title={selectedFAQ.question}
          size="xl"
        >
          <div className="space-y-4">
            <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
              <div className="p-3 bg-solar-100 rounded-lg">
                <selectedFAQ.icon className="h-6 w-6 text-solar-600" />
              </div>
              <div>
                <span className="text-sm font-medium text-solar-600 bg-solar-50 px-3 py-1 rounded-full">
                  {selectedFAQ.category}
                </span>
              </div>
            </div>

            <div className="prose prose-sm max-w-none">
              <div className="whitespace-pre-line text-gray-700 leading-relaxed">
                {selectedFAQ.answer}
              </div>
            </div>

            <div className="pt-4 border-t border-gray-200">
              <div className="flex flex-wrap gap-2">
                <span className="text-sm font-medium text-gray-700 mr-2">Tags:</span>
                {selectedFAQ.tags.map(tag => (
                  <span key={tag} className="text-sm text-solar-600 bg-solar-50 px-3 py-1 rounded-full">
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            <div className="pt-4 border-t border-gray-200">
              <p className="text-sm text-gray-600 mb-3">
                Still need help? Create a support ticket for personalized assistance.
              </p>
              <Button
                onClick={() => {
                  setSelectedFAQ(null);
                  setShowNewTicketModal(true);
                }}
                className="w-full sm:w-auto"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                Create Support Ticket
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};
