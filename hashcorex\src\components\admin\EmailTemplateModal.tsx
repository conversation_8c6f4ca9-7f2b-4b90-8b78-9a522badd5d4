'use client';

import React, { useState, useEffect } from 'react';
import { Button, Input, useMessageBox } from '@/components/ui';
import { X, Save, Eye } from 'lucide-react';

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface EmailTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  template?: EmailTemplate | null;
  onSave: () => void;
}

export const EmailTemplateModal: React.FC<EmailTemplateModalProps> = ({
  isOpen,
  onClose,
  template,
  onSave,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    htmlContent: '',
    textContent: '',
    isActive: true,
  });
  const [saving, setSaving] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const { showMessage } = useMessageBox();

  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent || '',
        isActive: template.isActive,
      });
    } else {
      setFormData({
        name: '',
        subject: '',
        htmlContent: '',
        textContent: '',
        isActive: true,
      });
    }
  }, [template]);

  const handleSave = async () => {
    if (!formData.name || !formData.subject || !formData.htmlContent) {
      showMessage({
        title: 'Validation Error',
        message: 'Name, subject, and HTML content are required',
        variant: 'error',
      });
      return;
    }

    try {
      setSaving(true);

      const url = template 
        ? `/api/admin/email-templates/${template.name}`
        : '/api/admin/email-templates';
      
      const method = template ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        showMessage({
          title: 'Success',
          message: template ? 'Template updated successfully' : 'Template created successfully',
          variant: 'success',
        });
        onSave();
        onClose();
      } else {
        throw new Error(data.error || 'Failed to save template');
      }
    } catch (error) {
      console.error('Failed to save template:', error);
      showMessage({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to save template',
        variant: 'error',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-700">
          <h2 className="text-xl font-semibold text-white">
            {template ? 'Edit Template' : 'Create Template'}
          </h2>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setPreviewMode(!previewMode)}
              variant="outline"
              size="sm"
              className="border-slate-600 text-slate-300"
            >
              <Eye className="w-4 h-4 mr-2" />
              {previewMode ? 'Edit' : 'Preview'}
            </Button>
            <button
              onClick={onClose}
              className="text-slate-400 hover:text-white"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {!previewMode ? (
            <div className="space-y-4">
              {/* Template Name */}
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Template Name *
                </label>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="e.g., otp_verification, welcome_email"
                  disabled={!!template} // Can't change name of existing template
                  className="bg-slate-700 border-slate-600 text-white"
                />
                <p className="text-xs text-slate-500 mt-1">
                  Use lowercase with underscores (e.g., otp_verification)
                </p>
              </div>

              {/* Subject */}
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Subject *
                </label>
                <Input
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  placeholder="Email subject line"
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>

              {/* HTML Content */}
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  HTML Content *
                </label>
                <textarea
                  value={formData.htmlContent}
                  onChange={(e) => handleInputChange('htmlContent', e.target.value)}
                  placeholder="HTML email content with variables like {{firstName}}, {{otp}}"
                  rows={12}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white font-mono text-sm"
                />
                <p className="text-xs text-slate-500 mt-1">
                  Use variables: {`{{firstName}}, {{lastName}}, {{otp}}, {{email}}`}
                </p>
              </div>

              {/* Text Content */}
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Text Content (Optional)
                </label>
                <textarea
                  value={formData.textContent}
                  onChange={(e) => handleInputChange('textContent', e.target.value)}
                  placeholder="Plain text version of the email"
                  rows={6}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white font-mono text-sm"
                />
              </div>

              {/* Active Status */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => handleInputChange('isActive', e.target.checked)}
                  className="rounded border-slate-600 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="isActive" className="text-sm text-slate-300">
                  Template is active
                </label>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-slate-900 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-white mb-2">Preview</h3>
                <div className="border border-slate-600 rounded-lg overflow-hidden">
                  <div className="bg-slate-700 px-4 py-2 border-b border-slate-600">
                    <p className="text-sm text-slate-300">
                      <strong>Subject:</strong> {formData.subject}
                    </p>
                  </div>
                  <div className="p-4 bg-white">
                    <div 
                      dangerouslySetInnerHTML={{ 
                        __html: formData.htmlContent.replace(/\{\{(\w+)\}\}/g, '<span style="background: yellow; padding: 2px 4px; border-radius: 3px;">$1</span>')
                      }} 
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-slate-700">
          <Button
            onClick={onClose}
            variant="outline"
            className="border-slate-600 text-slate-300"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {template ? 'Update' : 'Create'} Template
          </Button>
        </div>
      </div>
    </div>
  );
};
